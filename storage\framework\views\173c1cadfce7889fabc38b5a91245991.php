<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'dungeon',
    'userLevel' => 1,
    'userGearScore' => 0,
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'dungeon',
    'userLevel' => 1,
    'userGearScore' => 0,
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    // Проверяем доступность подземелья
    $canEnterByLevel = $dungeon->canPlayerEnterByLevel($userLevel);
    $isGearScoreRecommended = $dungeon->isGearScoreRecommended($userGearScore);
    
    // Определяем стили в зависимости от доступности (в стиле группы)
    $cardBorderClass = $canEnterByLevel ? 'border-[#514b3c] hover:border-[#a6925e]' : 'border-[#6c4539]';
    $cardBgClass = $canEnterByLevel ? 'bg-gradient-to-b from-[#3d3a2e] to-[#2a2721]' : 'bg-gradient-to-b from-[#2a1b12] to-[#1a0f0a]';
    
    // Стили для требований (более яркие цвета в стиле dark fantasy)
    $levelTextClass = $canEnterByLevel ? 'text-[#60d86e]' : 'text-[#ff7043]';
    $gsTextClass = $isGearScoreRecommended ? 'text-[#60d86e]' : 'text-[#ff7043]';
?>

<div class="dungeon-card <?php echo e($cardBgClass); ?> border-2 <?php echo e($cardBorderClass); ?> rounded-lg shadow-lg overflow-hidden transition-all duration-300">
    
    <div class="relative flex items-center p-2 border-b border-[#514b3c] overflow-hidden">
        
        <?php if($dungeon->image_path): ?>
            <div class="absolute inset-0 bg-cover bg-center"
                 style="background-image: url('<?php echo e(asset($dungeon->image_path)); ?>');">
                
                <div class="absolute inset-0 bg-gradient-to-r from-black/70 via-black/60 to-black/70"></div>
            </div>
        <?php else: ?>
            
            <div class="absolute inset-0 bg-gradient-to-r from-[#3e342c] via-[#2e2b24] to-[#3e342c]"></div>
        <?php endif; ?>
        
        
        <div class="absolute top-2 left-2 z-10">
            <?php if($canEnterByLevel): ?>
                <div class="w-3 h-3 bg-[#60d86e] rounded-full border border-[#4a9556] shadow-lg accessibility-indicator"></div>
            <?php else: ?>
                <div class="w-3 h-3 bg-[#ff7043] rounded-full border border-[#d84315] shadow-lg accessibility-indicator"></div>
            <?php endif; ?>
        </div>
        
        
        <div class="relative z-10 flex flex-col w-full">
            
            <div class="mb-2">
                <div class="inline-block bg-black/60 backdrop-blur-sm rounded-md px-3 py-1.5 border border-[#a6925e]/30">
                    <h3 class="text-[#f0e6b8] font-bold text-sm text-center leading-tight dungeon-title-glow">
                        <?php echo e($dungeon->name); ?>

                    </h3>
                </div>
            </div>
            
            
            <div class="flex items-center justify-between">
                <div class="flex gap-2 text-xs">
                    <span class="bg-black/50 backdrop-blur-sm rounded px-2 py-0.5 border border-[#514b3c]/50">
                        <span class="<?php echo e($levelTextClass); ?> font-bold">ур.<?php echo e($dungeon->min_level); ?></span>
                    </span>
                    <span class="bg-black/50 backdrop-blur-sm rounded px-2 py-0.5 border border-[#514b3c]/50">
                        <span class="text-[#e8d5a5]"><?php echo e($dungeon->players_range); ?></span>
                    </span>
                    <span class="bg-black/50 backdrop-blur-sm rounded px-2 py-0.5 border border-[#514b3c]/50">
                        <span class="<?php echo e($gsTextClass); ?> font-bold">GS <?php echo e($dungeon->recommended_gs); ?></span>
                    </span>
                </div>
                
                
                <div class="flex-shrink-0">
                    <?php if($canEnterByLevel): ?>
                        <form action="<?php echo e(route('dungeons.lobby', $dungeon)); ?>" method="GET" class="inline-block">
                            <button type="submit" class="bg-[#2a2621]/90 backdrop-blur-sm border border-[#a6925e]/60 text-[#f0e6b8] 
                                         font-bold py-1.5 px-3 rounded text-xs hover:border-[#a6925e] hover:bg-[#2a2621] hover:text-[#e5b769]
                                         transition-all duration-300 shadow-lg dungeon-button-glow">
                                Войти
                            </button>
                        </form>
                    <?php else: ?>
                        <button disabled class="bg-[#2a1b12]/80 backdrop-blur-sm border border-[#6c4539] text-[#a68a68] 
                                             font-semibold py-1.5 px-3 rounded text-xs cursor-not-allowed opacity-75 shadow-lg">
                            Закрыто
                        </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    
    <div class="px-2 py-1">
        <div class="compact-rewards-carousel" data-dungeon-id="<?php echo e($dungeon->id); ?>">
            <?php if($dungeon->getAllRewards()->count() > 0): ?>
                <div class="text-xs text-[#9a9483] mb-1">Награды:</div>
                <div class="relative">
                    
                    <div class="rewards-scroll-container overflow-hidden">
                        <div class="rewards-track flex gap-1 transition-transform duration-300 ease-in-out">
                            <?php $__currentLoopData = $dungeon->getAllRewards(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $reward): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    // Определяем тип награды и иконку
                                    $isUniversal = $reward instanceof \App\Models\DungeonUniversalReward;
                                    
                                    if ($isUniversal) {
                                        $rewardName = $reward->display_name;
                                        $rewardIcon = $reward->display_icon;
                                        $dropChance = $reward->drop_chance;
                                        $isIconEmoji = in_array($rewardIcon, ['🪙', '🥈', '🥉', '💰', '⭐', '❓']);
                                    } else {
                                        $rewardName = $reward->name ?? 'Предмет';
                                        $rewardIcon = $reward->icon ?? null;
                                        $dropChance = isset($reward->pivot) ? $reward->pivot->drop_chance : null;
                                        $isIconEmoji = false;
                                    }
                                ?>
                                
                                <div class="reward-icon-item cursor-pointer" 
                                     title="<?php echo e($rewardName); ?><?php echo e($dropChance ? ' (' . number_format($dropChance, 1) . '%)' : ''); ?>"
                                     data-reward-modal
                                     data-reward-name="<?php echo e($rewardName); ?>"
                                     data-reward-icon="<?php echo e($rewardIcon); ?>"
                                     data-reward-drop-chance="<?php echo e($dropChance ?? 0); ?>"
                                     data-reward-type="<?php echo e($isUniversal ? 'universal' : 'legacy'); ?>"
                                     data-reward-quantity="<?php echo e($isUniversal ? ($reward->display_quantity ?? 1) : 1); ?>"
                                     data-reward-quality="<?php echo e($isUniversal ? ($reward->quality ?? 'Обычное') : 'Обычное'); ?>"
                                     data-reward-level="<?php echo e($isUniversal ? ($reward->level ?? $dungeon->min_level) : $dungeon->min_level); ?>">
                                    <div class="w-6 h-6 bg-[#2a2621] border border-[#514b3c] rounded flex items-center justify-center hover:border-[#a6925e] hover:scale-110 transition-all duration-200">
                                        <?php if($rewardIcon && !$isIconEmoji): ?>
                                            <img src="<?php echo e(asset($rewardIcon)); ?>"
                                                 alt="<?php echo e($rewardName); ?>"
                                                 class="w-4 h-4 object-contain"
                                                 loading="lazy">
                                        <?php elseif($isIconEmoji): ?>
                                            <div class="text-xs"><?php echo e($rewardIcon); ?></div>
                                        <?php else: ?>
                                            <div class="text-xs text-[#8c7a55]">⚔️</div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                    
                    
                    <?php if($dungeon->getAllRewards()->count() > 4): ?>
                        <button class="rewards-nav-prev absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1
                                     w-4 h-4 bg-[#2a2621] border border-[#514b3c] rounded-full
                                     flex items-center justify-center text-xs text-[#a6925e]
                                     hover:border-[#a6925e] hover:bg-[#3a3229] transition-all duration-200 z-10
                                     opacity-50 disabled:opacity-30 disabled:cursor-not-allowed"
                                data-dungeon-id="<?php echo e($dungeon->id); ?>" 
                                data-direction="prev"
                                disabled>
                            ←
                        </button>
                        
                        <button class="rewards-nav-next absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-1
                                     w-4 h-4 bg-[#2a2621] border border-[#514b3c] rounded-full
                                     flex items-center justify-center text-xs text-[#a6925e]
                                     hover:border-[#a6925e] hover:bg-[#3a3229] transition-all duration-200 z-10
                                     opacity-50 disabled:opacity-30 disabled:cursor-not-allowed"
                                data-dungeon-id="<?php echo e($dungeon->id); ?>" 
                                data-direction="next">
                            →
                        </button>
                    <?php endif; ?>
                </div>
            <?php else: ?>
                <div class="text-xs text-[#8c7a55] text-center py-1">Награды не указаны</div>
            <?php endif; ?>
        </div>
    </div>
</div>


<div id="reward-modal-<?php echo e($dungeon->id); ?>" class="reward-modal fixed inset-0 bg-black/70 backdrop-blur-sm z-50 hidden flex items-center justify-center p-4">
    <div class="modal-content bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] border-2 border-[#a6925e] rounded-lg shadow-2xl max-w-sm w-full mx-4 transform scale-95 transition-all duration-300">
        
        <div class="modal-header bg-[#2a2621] border-b border-[#514b3c] px-4 py-3 rounded-t-lg">
            <div class="flex items-center justify-between">
                <h3 class="modal-title text-[#e5b769] font-semibold text-lg"></h3>
                <button class="modal-close text-[#8c7a55] hover:text-[#e5b769] text-xl font-bold transition-colors duration-200">&times;</button>
            </div>
        </div>
        
        
        <div class="modal-body p-4">
            <div class="flex items-start gap-4">
                
                <div class="modal-icon flex-shrink-0">
                    <div class="w-16 h-16 bg-[#2a2621] border-2 border-[#a6925e] rounded-lg flex items-center justify-center">
                        <img class="modal-reward-icon w-12 h-12 object-contain" src="" alt="">
                    </div>
                </div>
                
                
                <div class="modal-info flex-1">
                    
                    <div class="mb-2">
                        <span class="text-[#9a9483] text-xs">Качество:</span>
                        <div class="modal-quality text-[#fceac4] font-medium"></div>
                    </div>
                    
                    
                    <div class="mb-2">
                        <span class="text-[#9a9483] text-xs">Уровень:</span>
                        <div class="modal-level text-[#fceac4] font-medium"></div>
                    </div>
                    
                    
                    <div class="mb-2">
                        <span class="text-[#9a9483] text-xs">Количество:</span>
                        <div class="modal-quantity text-[#fceac4] font-medium"></div>
                    </div>
                    
                    
                    <div class="mb-2">
                        <span class="text-[#9a9483] text-xs">Шанс выпадения:</span>
                        <div class="modal-drop-chance text-[#7cfc00] font-bold"></div>
                    </div>
                </div>
            </div>
        </div>
        
        
        <div class="modal-footer bg-[#2a2621] border-t border-[#514b3c] px-4 py-3 rounded-b-lg text-center">
            <button class="modal-close-btn bg-[#514b3c] hover:bg-[#a6925e] text-[#fceac4] px-4 py-2 rounded text-sm font-medium transition-all duration-200">
                Закрыть
            </button>
        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/dungeons/dungeon-card.blade.php ENDPATH**/ ?>