<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['guildInvitation']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['guildInvitation']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>



<?php if($guildInvitation): ?>
    
    <div class="relative bg-[#2a2621] border border-[#a6925e] rounded-md mb-3 mt-2 overflow-hidden shadow-md invitation-card-glow">
        
        <div class="absolute top-0 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-60"></div>
        <div class="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-60"></div>

        <div class="p-3">
            
            <div class="flex items-center mb-2">
                
                <div class="flex-shrink-0 mr-3">
                    <?php if($guildInvitation->guild->icon): ?>
                        <img src="<?php echo e(asset($guildInvitation->guild->icon)); ?>" alt="<?php echo e($guildInvitation->guild->name); ?>"
                            class="w-12 h-12 rounded-md border border-[#514b3c] shadow-sm object-cover">
                    <?php else: ?>
                        <div class="w-12 h-12 rounded-md border border-[#514b3c] bg-[#38352c] flex items-center justify-center text-[#e5b769] text-xl shadow-sm">
                            <span>G</span>
                        </div>
                    <?php endif; ?>
                </div>

                
                <div class="flex-1">
                    <p class="text-[#e5b769] text-sm">
                        <span class="font-medium"><?php echo e($guildInvitation->inviter->name); ?></span>
                    </p>
                    <p class="text-[#d3c6a6] text-sm">
                        приглашает вас вступить в гильдию <span class="text-[#e5b769] font-medium"><?php echo e($guildInvitation->guild->name); ?></span>
                    </p>
                </div>
            </div>

            
            <div class="flex justify-center space-x-4">
                <form action="<?php echo e(route('guilds.invitations.accept', $guildInvitation)); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <button type="submit"
                        class="py-1.5 px-6 bg-[#2a3b2a] border border-[#4a6e4a] rounded-md text-[#a3d9a3] hover:bg-[#2f4a2f] transition-all duration-300">
                        Принять
                    </button>
                </form>
                <form action="<?php echo e(route('guilds.invitations.decline', $guildInvitation)); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <button type="submit"
                        class="py-1.5 px-6 bg-[#3b2a2a] border border-[#6e4a4a] rounded-md text-[#d9a3a3] hover:bg-[#4a2f2f] transition-all duration-300">
                        Отклонить
                    </button>
                </form>
            </div>
        </div>
    </div>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/layout/guild-invitation.blade.php ENDPATH**/ ?>