@php
    // Компонент для отображения flash сообщений стана в том же стиле что и обычные flash сообщения
    $stunMessage = session('stun_error') ?? session('error');
    $isStunMessage = session('stun_error') || ($stunMessage && (
        str_contains($stunMessage, 'оглушен') ||
        str_contains($stunMessage, 'стан') ||
        str_contains($stunMessage, '⚡')
    ));
@endphp

@if($isStunMessage)
    {{-- Отображаем stun сообщение в том же стиле что и обычные flash сообщения --}}
    <div
        class="bg-gradient-to-r from-red-800/90 to-red-700/90 text-red-100 p-1.5 mx-0 my-1.5 text-xs rounded border border-red-600/50 shadow-inner shadow-black/30 mb-2 animate-fade-in">
        <span class="mr-1">⚡</span>
        {!! $stunMessage !!}
    </div>
@endif