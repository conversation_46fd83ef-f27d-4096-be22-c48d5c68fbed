/**
 * Система управления эффектами оглушения
 * Блокирует интерфейс при активных эффектах оглушения
 */

class StunManager {
    constructor() {
        this.isStunned = false;
        this.stunCheckInterval = null;
        this.blockedElements = new Set();
        this.originalHandlers = new Map();

        this.init();
    }

    /**
     * Инициализация системы
     */
    init() {
        this.checkStunStatus();
        this.startPeriodicCheck();
        this.setupEventListeners();

        console.log("StunManager инициализирован");
    }

    /**
     * Запуск периодической проверки состояния оглушения
     */
    startPeriodicCheck() {
        // Проверяем каждые 2 секунды
        this.stunCheckInterval = setInterval(() => {
            this.checkStunStatus();
        }, 2000);
    }

    /**
     * Остановка периодической проверки
     */
    stopPeriodicCheck() {
        if (this.stunCheckInterval) {
            clearInterval(this.stunCheckInterval);
            this.stunCheckInterval = null;
        }
    }

    /**
     * Проверка текущего состояния оглушения
     */
    async checkStunStatus() {
        try {
            const response = await fetch("/api/user/effects/stun-status", {
                method: "GET",
                headers: {
                    "X-CSRF-TOKEN": document
                        .querySelector('meta[name="csrf-token"]')
                        .getAttribute("content"),
                    Accept: "application/json",
                },
            });

            if (response.ok) {
                const data = await response.json();
                this.updateStunStatus(data.is_stunned, data.stun_message);
            }
        } catch (error) {
            console.error("Ошибка при проверке состояния оглушения:", error);
        }
    }

    /**
     * Обновление состояния оглушения
     */
    updateStunStatus(isStunned, stunMessage = null) {
        if (this.isStunned !== isStunned) {
            this.isStunned = isStunned;

            if (isStunned) {
                this.blockInterface(stunMessage);
            } else {
                this.unblockInterface();
            }
        }
    }

    /**
     * Блокировка интерфейса
     */
    blockInterface(message = "⚡ Вы оглушены и не можете действовать!") {
        console.log("Блокировка интерфейса из-за оглушения");

        // Блокируем формы
        this.blockForms();

        // Блокируем кнопки
        this.blockButtons();

        // Блокируем ссылки навигации
        this.blockNavigationLinks();

        // Показываем сообщение об оглушении
        this.showStunMessage(message);

        // Добавляем визуальные эффекты
        this.addStunVisualEffects();
    }

    /**
     * Разблокировка интерфейса
     */
    unblockInterface() {
        console.log("Разблокировка интерфейса");

        // Разблокируем все заблокированные элементы
        this.unblockAllElements();

        // Скрываем сообщение об оглушении
        this.hideStunMessage();

        // Убираем визуальные эффекты
        this.removeStunVisualEffects();

        // Показываем сообщение о необходимости обновления страницы
        this.showPageRefreshMessage();
    }

    /**
     * Блокировка форм
     */
    blockForms() {
        const forms = document.querySelectorAll("form");
        forms.forEach((form) => {
            if (!form.classList.contains("stun-blocked")) {
                this.blockElement(form);

                // Блокируем отправку формы
                const submitHandler = (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.showStunNotification();
                    return false;
                };

                form.addEventListener("submit", submitHandler);
                this.originalHandlers.set(form, {
                    type: "submit",
                    handler: submitHandler,
                });
            }
        });
    }

    /**
     * Блокировка кнопок
     */
    blockButtons() {
        const buttons = document.querySelectorAll(
            'button, input[type="submit"], input[type="button"]'
        );
        buttons.forEach((button) => {
            // Проверяем, должна ли кнопка быть заблокирована
            if (
                !button.classList.contains("stun-blocked") &&
                this.shouldBlockButton(button)
            ) {
                this.blockElement(button);

                const clickHandler = (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.showStunNotification();
                    return false;
                };

                button.addEventListener("click", clickHandler);
                this.originalHandlers.set(button, {
                    type: "click",
                    handler: clickHandler,
                });
            }
        });
    }

    /**
     * Проверяет, должна ли кнопка быть заблокирована при стане
     */
    shouldBlockButton(button) {
        // Разрешенные классы кнопок (навигация)
        const allowedClasses = [
            "navigation-button-link",
            "nav-button",
            "menu-button",
            "tab-button",
            "view-button",
        ];

        // Проверяем классы кнопки
        if (
            allowedClasses.some((className) =>
                button.classList.contains(className)
            )
        ) {
            return false;
        }

        // Разрешенные типы кнопок по тексту
        const buttonText = button.textContent?.trim().toLowerCase() || "";
        const allowedButtonTexts = [
            "персонаж",
            "рюкзак",
            "гильдия",
            "сообщения",
            "настройки",
            "выход",
            "главная",
            "битва",
            "аванпосты",
            "рудники",
            "подземелья",
            "группы",
            "магазин",
            "таверна",
            "профессии",
            "фермерство",
        ];

        if (allowedButtonTexts.some((text) => buttonText.includes(text))) {
            return false;
        }

        // Разрешенные data-атрибуты
        const allowedDataActions = ["navigate", "view", "show", "display"];

        const dataAction = button.getAttribute("data-action");
        if (dataAction && allowedDataActions.includes(dataAction)) {
            return false;
        }

        // Блокируем все остальные кнопки (боевые действия, торговля и т.д.)
        return true;
    }

    /**
     * Блокировка навигационных ссылок
     */
    blockNavigationLinks() {
        // Блокируем основные навигационные ссылки
        const navSelectors = [
            'a[href]:not([href^="#"]):not([href^="javascript:"])', // Все ссылки кроме якорей и JS
            ".navigation-button-link", // Основная навигация
            'a[href*="/battle/"]', // Боевые локации
            'a[href*="/dungeons/"]', // Подземелья
            'a[href*="/shop"]', // Магазин
            'a[href*="/masters"]', // Таверна
            'a[href*="/farming"]', // Фермерство
            'a[href*="/professions"]', // Профессии
            'a[href*="/square"]', // Площадь
            'a[href*="/party"]', // Группы
            'a[href*="/guilds"]', // Гильдии
        ];

        navSelectors.forEach((selector) => {
            const links = document.querySelectorAll(selector);
            links.forEach((link) => {
                // Исключаем некоторые ссылки, которые должны оставаться доступными
                const href = link.getAttribute("href") || "";
                const isAllowed = this.isLinkAllowed(href);

                if (!isAllowed && !link.classList.contains("stun-blocked")) {
                    this.blockElement(link);

                    const clickHandler = (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        this.showStunNotification();
                        return false;
                    };

                    link.addEventListener("click", clickHandler, true); // Используем capture phase
                    this.originalHandlers.set(link, {
                        type: "click",
                        handler: clickHandler,
                    });
                }
            });
        });

        // Дополнительно блокируем специфичные компоненты интерфейса
        this.blockInterfaceComponents();
    }

    /**
     * Проверяет, разрешена ли ссылка при стане
     */
    isLinkAllowed(href) {
        const allowedPatterns = [
            "/inventory", // Рюкзак
            "/character", // Персонаж
            "/user/profile", // Профиль
            "/messages", // Сообщения
            "/settings", // Настройки
            "/auth/logout", // Выход
            "/battle/defeat", // Страница поражения
            "/battle/respawn", // Возрождение
            "/home", // Главная страница
        ];

        // Проверяем разрешенные паттерны
        if (allowedPatterns.some((pattern) => href.includes(pattern))) {
            return true;
        }

        // Разрешаем просмотр локаций (но НЕ действия в них)
        const viewOnlyPatterns = [
            "/battle$", // Главная страница битвы
            "/battle/$", // Главная страница битвы
            "/battle/outposts$", // Список аванпостов
            "/battle/outposts/$", // Список аванпостов
            "/battle/mines$", // Список рудников
            "/battle/mines/$", // Список рудников
            "/dungeons$", // Список подземелий
            "/dungeons/$", // Список подземелий
            "/party$", // Просмотр группы
            "/party/$", // Просмотр группы
            "/shop$", // Просмотр магазина
            "/shop/$", // Просмотр магазина
            "/masters$", // Просмотр таверны
            "/masters/$", // Просмотр таверны
            "/professions$", // Просмотр профессий
            "/professions/$", // Просмотр профессий
            "/farming$", // Просмотр фермерства
            "/farming/$", // Просмотр фермерства
        ];

        // Проверяем паттерны для просмотра (точное совпадение с концом URL)
        if (
            viewOnlyPatterns.some((pattern) => {
                const regex = new RegExp(pattern);
                return regex.test(href);
            })
        ) {
            return true;
        }

        // Разрешаем просмотр конкретных локаций рудников (но НЕ действия)
        const mineViewPattern = /^\/battle\/mines\/[^\/]+$/;
        if (mineViewPattern.test(href)) {
            return true;
        }

        // Разрешаем просмотр конкретных аванпостов (но НЕ действия)
        const outpostViewPattern = /^\/battle\/outposts\/[^\/]+$/;
        if (outpostViewPattern.test(href)) {
            return true;
        }

        // Разрешаем просмотр конкретных подземелий (но НЕ действия)
        const dungeonViewPattern = /^\/dungeons\/[^\/]+$/;
        if (dungeonViewPattern.test(href)) {
            return true;
        }

        return false;
    }

    /**
     * Блокировка отдельного элемента
     */
    blockElement(element) {
        element.classList.add("stun-blocked");
        element.style.pointerEvents = "none";
        element.style.opacity = "0.5";
        element.style.cursor = "not-allowed";
        element.style.filter = "grayscale(0.8) brightness(0.6) contrast(0.8)";
        element.style.transition = "all 0.3s ease";

        // Добавляем визуальный индикатор блокировки
        if (!element.querySelector(".stun-overlay")) {
            const overlay = document.createElement("div");
            overlay.className = "stun-overlay";
            overlay.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(26, 24, 20, 0.4);
                border: 1px solid rgba(59, 54, 41, 0.6);
                pointer-events: none;
                z-index: 1000;
                animation: stunPulse 2s infinite;
                backdrop-filter: blur(1px);
            `;

            // Делаем элемент относительно позиционированным для overlay
            if (getComputedStyle(element).position === "static") {
                element.style.position = "relative";
            }

            element.appendChild(overlay);
        }

        this.blockedElements.add(element);
    }

    /**
     * Разблокировка всех элементов
     */
    unblockAllElements() {
        this.blockedElements.forEach((element) => {
            element.classList.remove("stun-blocked");
            element.style.pointerEvents = "";
            element.style.opacity = "";
            element.style.cursor = "";
            element.style.filter = "";
            element.style.transition = "";

            // Удаляем overlay
            const overlay = element.querySelector(".stun-overlay");
            if (overlay) {
                overlay.remove();
            }

            // Восстанавливаем позиционирование, если мы его меняли
            if (
                element.style.position === "relative" &&
                !element.dataset.originalPosition
            ) {
                element.style.position = "";
            }
        });

        // Удаляем обработчики событий
        this.originalHandlers.forEach((handlerInfo, element) => {
            element.removeEventListener(
                handlerInfo.type,
                handlerInfo.handler,
                true
            );
        });

        this.blockedElements.clear();
        this.originalHandlers.clear();
    }

    /**
     * Показать сообщение об оглушении
     */
    showStunMessage(message) {
        // Удаляем старое сообщение, если есть
        this.hideStunMessage();

        const stunMessage = document.createElement("div");
        stunMessage.id = "stun-message";
        stunMessage.className =
            "fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-gradient-to-r from-[#6e3f35] to-[#59372d] text-[#e4d7b0] px-6 py-3 rounded-lg shadow-lg border-2 border-[#3b3629] backdrop-blur-sm";
        stunMessage.innerHTML = `
            <div class="flex items-center space-x-2">
                <span class="text-xl text-[#c1a96e]">⚡</span>
                <span class="font-medium drop-shadow-[0_1px_1px_rgba(0,0,0,0.8)]">${message}</span>
            </div>
        `;

        document.body.appendChild(stunMessage);
    }

    /**
     * Скрыть сообщение об оглушении
     */
    hideStunMessage() {
        const stunMessage = document.getElementById("stun-message");
        if (stunMessage) {
            stunMessage.remove();
        }
    }

    /**
     * Показать уведомление при попытке действия
     */
    showStunNotification() {
        // Создаем временное уведомление
        const notification = document.createElement("div");
        notification.className =
            "fixed top-16 left-1/2 transform -translate-x-1/2 z-50 bg-gradient-to-r from-[#6e3f35] to-[#59372d] text-[#e4d7b0] px-4 py-2 rounded shadow-lg border border-[#3b3629] backdrop-blur-sm";
        notification.innerHTML = `
            <div class="flex items-center space-x-2">
                <span class="text-[#c1a96e]">⚠️</span>
                <span class="drop-shadow-[0_1px_1px_rgba(0,0,0,0.8)]">Действие заблокировано - вы оглушены!</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Удаляем через 2 секунды
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 2000);
    }

    /**
     * Показать сообщение о необходимости обновления страницы
     */
    showPageRefreshMessage() {
        // Удаляем старое сообщение, если есть
        const existingMessage = document.getElementById("stun-refresh-message");
        if (existingMessage) {
            existingMessage.remove();
        }

        const refreshMessage = document.createElement("div");
        refreshMessage.id = "stun-refresh-message";
        refreshMessage.className =
            "fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-gradient-to-r from-[#2f473c] to-[#1e2e27] text-[#e4d7b0] px-6 py-4 rounded-lg shadow-lg border-2 border-[#3b3629] backdrop-blur-sm max-w-md";
        refreshMessage.innerHTML = `
            <div class="text-center">
                <div class="flex items-center justify-center space-x-2 mb-2">
                    <span class="text-xl text-[#c1a96e]">🔄</span>
                    <span class="font-medium drop-shadow-[0_1px_1px_rgba(0,0,0,0.8)]">Стан окончен!</span>
                </div>
                <p class="text-sm text-[#d4cbb0] mb-3 drop-shadow-[0_1px_1px_rgba(0,0,0,0.8)]">
                    Обновите страницу для продолжения игры
                </p>
                <button onclick="location.reload()"
                        class="bg-gradient-to-b from-[#2f473c] to-[#1e2e27] hover:from-[#3a5847] hover:to-[#243530]
                               text-[#f8eac2] px-4 py-2 rounded border border-[#3b3629]
                               transition-all duration-200 drop-shadow-[0_2px_4px_rgba(0,0,0,0.3)]">
                    Обновить страницу
                </button>
            </div>
        `;

        document.body.appendChild(refreshMessage);
    }

    /**
     * Блокировка специфичных компонентов интерфейса
     */
    blockInterfaceComponents() {
        // Селекторы для блокировки компонентов интерфейса
        const componentSelectors = [
            // Хлебные крошки
            ".breadcrumbs",
            ".forum-navigation",
            'nav[class*="breadcrumb"]',

            // Панель скиллов
            ".skills-panel",
            ".battle-skills",
            '[class*="skill"]',

            // Панель действий
            ".actions-panel",
            ".battle-actions",
            ".target-actions",

            // Навигационные кнопки
            ".navigation-buttons",
            ".nav-buttons",
            ".game-navigation",

            // Панель быстрого использования
            ".quick-potion-bar",
            ".quick-actions",

            // Формы действий
            'form[action*="attack"]',
            'form[action*="use"]',
            'form[action*="select"]',
            'form[action*="hit"]',

            // Кнопки действий
            'button[type="submit"]',
            'input[type="submit"]',

            // Ссылки действий
            'a[href*="attack"]',
            'a[href*="use"]',
            'a[href*="select"]',
            'a[href*="hit"]',
        ];

        componentSelectors.forEach((selector) => {
            const elements = document.querySelectorAll(selector);
            elements.forEach((element) => {
                if (!element.classList.contains("stun-blocked")) {
                    this.blockElement(element);

                    // Добавляем обработчик для предотвращения действий
                    const preventHandler = (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        this.showStunNotification();
                        return false;
                    };

                    // Блокируем различные события
                    ["click", "submit", "change"].forEach((eventType) => {
                        element.addEventListener(
                            eventType,
                            preventHandler,
                            true
                        );
                        this.originalHandlers.set(element, {
                            type: eventType,
                            handler: preventHandler,
                        });
                    });
                }
            });
        });
    }

    /**
     * Добавить визуальные эффекты оглушения
     */
    addStunVisualEffects() {
        document.body.classList.add("stunned");

        // Добавляем CSS стили, если их еще нет
        if (!document.getElementById("stun-styles")) {
            const style = document.createElement("style");
            style.id = "stun-styles";
            style.textContent = `
                .stunned {
                    filter: saturate(0.6) brightness(0.8) contrast(0.9);
                }
                .stun-blocked {
                    transition: all 0.3s ease;
                }
                @keyframes stunPulse {
                    0% {
                        background-color: rgba(26, 24, 20, 0.3);
                        border-color: rgba(59, 54, 41, 0.5);
                        box-shadow: inset 0 0 10px rgba(26, 24, 20, 0.4);
                    }
                    50% {
                        background-color: rgba(26, 24, 20, 0.5);
                        border-color: rgba(59, 54, 41, 0.7);
                        box-shadow: inset 0 0 15px rgba(26, 24, 20, 0.6);
                    }
                    100% {
                        background-color: rgba(26, 24, 20, 0.3);
                        border-color: rgba(59, 54, 41, 0.5);
                        box-shadow: inset 0 0 10px rgba(26, 24, 20, 0.4);
                    }
                }
                .stun-overlay {
                    animation: stunPulse 2s infinite;
                }
            `;
            document.head.appendChild(style);
        }
    }

    /**
     * Убрать визуальные эффекты оглушения
     */
    removeStunVisualEffects() {
        document.body.classList.remove("stunned");
    }

    /**
     * Настройка обработчиков событий
     */
    setupEventListeners() {
        // Обработчик для обновления страницы
        window.addEventListener("beforeunload", () => {
            this.stopPeriodicCheck();
        });

        // Обработчик для фокуса/потери фокуса окна
        document.addEventListener("visibilitychange", () => {
            if (!document.hidden) {
                this.checkStunStatus();
            }
        });
    }

    /**
     * Принудительная проверка состояния
     */
    forceCheck() {
        this.checkStunStatus();
    }

    /**
     * Получить текущее состояние
     */
    getStunStatus() {
        return this.isStunned;
    }

    /**
     * Уничтожение экземпляра
     */
    destroy() {
        this.stopPeriodicCheck();
        this.unblockInterface();
        this.removeStunVisualEffects();

        const stunStyles = document.getElementById("stun-styles");
        if (stunStyles) {
            stunStyles.remove();
        }
    }
}

// Создаем глобальный экземпляр
window.StunManager = new StunManager();

// Экспорт для модульной системы
export default StunManager;
