

<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['announcementData' => null, 'unviewedAnnouncements' => collect()]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['announcementData' => null, 'unviewedAnnouncements' => collect()]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    // Определяем данные для отображения
    if ($announcementData && isset($announcementData['latest_announcement']) && $announcementData['latest_announcement']) {
        $latestAnnouncement = $announcementData['latest_announcement'];
        $totalCount = $announcementData['total_count'];
        $hasAnnouncements = true;
    } elseif ($unviewedAnnouncements->isNotEmpty()) {
        // Обратная совместимость со старым форматом
        $latestAnnouncement = $unviewedAnnouncements->first();
        $totalCount = $unviewedAnnouncements->count();
        $hasAnnouncements = true;
    } else {
        $hasAnnouncements = false;
    }
?>

<?php if($hasAnnouncements): ?>
    <div class="mb-3 bg-gradient-to-r from-[#593d5e] to-[#3e2d42] p-3 rounded-lg border border-[#a6925e] shadow-lg">
        
        <div class="flex items-center justify-between mb-2">
            <div class="flex items-center">
                <span class="text-xl mr-2">📜</span>
                <h3 class="text-[#e5b769] font-medium text-sm">
                    <?php if($totalCount === 1): ?>
                        Новое объявление
                    <?php else: ?>
                        Новые объявления
                    <?php endif; ?>
                </h3>
            </div>
            <div class="flex items-center space-x-2">
                <span class="bg-[#e74c3c] text-white text-xs px-2 py-1 rounded-full animate-pulse">
                    <?php echo e($totalCount); ?>

                </span>
                <?php if($totalCount > 1): ?>
                    <span class="text-[#a6925e] text-xs">
                        +<?php echo e($totalCount - 1); ?> ещё
                    </span>
                <?php endif; ?>
            </div>
        </div>

        
        <div class="space-y-2">
            <a href="<?php echo e(route('forum.news.show', $latestAnnouncement->id)); ?>"
               class="block bg-[#2d2731] p-2 rounded border border-[#514b3c] hover:border-[#a6925e] transition-all duration-200 group">
                <div class="flex items-start">
                    
                    <div class="w-8 h-8 bg-[#593d5e] text-white rounded flex items-center justify-center mr-2 flex-shrink-0 border border-[#a6925e] group-hover:bg-[#6b4a6f] transition-colors">
                        <?php if($latestAnnouncement->announcement_type): ?>
                            <?php switch($latestAnnouncement->announcement_type):
                                case ('event'): ?>
                                    <span class="text-sm">🎉</span>
                                    <?php break; ?>
                                <?php case ('action'): ?>
                                    <span class="text-sm">🔥</span>
                                    <?php break; ?>
                                <?php case ('holiday'): ?>
                                    <span class="text-sm">🎊</span>
                                    <?php break; ?>
                                <?php case ('system'): ?>
                                    <span class="text-sm">⚙️</span>
                                    <?php break; ?>
                                <?php default: ?>
                                    <span class="text-sm">📜</span>
                            <?php endswitch; ?>
                        <?php else: ?>
                            <span class="text-sm">📜</span>
                        <?php endif; ?>
                    </div>

                    <div class="flex-1 min-w-0">
                        
                        <h4 class="text-[#e5b769] font-medium text-sm mb-1 group-hover:text-[#f0d89e] transition-colors truncate">
                            <?php echo e($latestAnnouncement->title); ?>

                        </h4>

                        
                        <div class="flex items-center justify-between">
                            <span class="text-[#a6925e] text-xs">
                                <?php echo e($latestAnnouncement->created_at->format('d.m.Y H:i')); ?>

                            </span>

                            
                            <?php if($latestAnnouncement->announcement_type): ?>
                                <?php
                                    $typeClass = '';
                                    $typeText = '';

                                    switch ($latestAnnouncement->announcement_type) {
                                        case 'event':
                                            $typeClass = 'bg-[#9370db]';
                                            $typeText = 'Событие';
                                            break;
                                        case 'action':
                                            $typeClass = 'bg-[#ff9800]';
                                            $typeText = 'Акция';
                                            break;
                                        case 'holiday':
                                            $typeClass = 'bg-[#00bcd4]';
                                            $typeText = 'Праздник';
                                            break;
                                        case 'system':
                                            $typeClass = 'bg-[#607d8b]';
                                            $typeText = 'Система';
                                            break;
                                    }
                                ?>

                                <?php if($typeText): ?>
                                    <span class="<?php echo e($typeClass); ?> text-white text-xs px-1 py-0.5 rounded whitespace-nowrap">
                                        <?php echo e($typeText); ?>

                                    </span>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>

                        
                        <div class="flex items-center mt-1 space-x-1">
                            <?php if($latestAnnouncement->is_pinned): ?>
                                <span class="bg-[#e5b769] text-[#2d2a22] text-xs px-1 py-0.5 rounded font-medium">
                                    📌 Закреплено
                                </span>
                            <?php endif; ?>

                            <?php if($latestAnnouncement->is_important): ?>
                                <span class="bg-[#e74c3c] text-white text-xs px-1 py-0.5 rounded font-medium animate-pulse">
                                    ⚠️ Важно
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </a>
        </div>

        
        <div class="mt-3 pt-2 border-t border-[#514b3c]">
            <a href="<?php echo e(route('forum.news.index', ['type' => 'announcements'])); ?>"
               class="block text-center text-[#a6925e] hover:text-[#e5b769] text-xs transition-colors">
                <?php if($totalCount > 1): ?>
                    Посмотреть все <?php echo e($totalCount); ?> объявлений →
                <?php else: ?>
                    Посмотреть объявление →
                <?php endif; ?>
            </a>
        </div>
    </div>
<?php endif; ?>

<style>
    /* Анимация для блока объявлений */
    @keyframes announcementGlow {
        0% {
            box-shadow: 0 0 5px rgba(165, 146, 94, 0.3);
        }
        50% {
            box-shadow: 0 0 15px rgba(165, 146, 94, 0.6);
        }
        100% {
            box-shadow: 0 0 5px rgba(165, 146, 94, 0.3);
        }
    }

    /* Применяем анимацию к блоку с объявлениями */
    .announcement-block {
        animation: announcementGlow 3s ease-in-out infinite;
    }
</style>
<?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/layout/announcement-notifications.blade.php ENDPATH**/ ?>