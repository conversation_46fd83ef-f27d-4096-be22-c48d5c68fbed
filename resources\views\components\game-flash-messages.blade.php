@php
    // Определяем классы для разных типов сообщений в игровом стиле
    $messageTypes = [
        'success' => [
            'bg' => 'bg-[#36513f]',
            'text' => 'text-[#c8ffdb]',
            'border' => 'border-[#4a7759]',
            'icon' => '✅',
        ],
        'error' => [
            'bg' => 'bg-[#613f36]',
            'text' => 'text-[#ffeac1]',
            'border' => 'border-[#88634a]',
            'icon' => '❌',
        ],
        'warning' => [
            'bg' => 'bg-[#5e553a]',
            'text' => 'text-[#ffe7bd]',
            'border' => 'border-[#7d7248]',
            'icon' => '⚠️',
        ],
        'info' => [
            'bg' => 'bg-[#3a4a5e]',
            'text' => 'text-[#c1dcff]',
            'border' => 'border-[#4a617d]',
            'icon' => 'ℹ️',
        ],
        'party_action' => [
            'bg' => 'bg-[#613f36]',
            'text' => 'text-[#ffcbb0]',
            'border' => 'border-[#88634a]',
            'icon' => '👥',
        ],
    ];

    // Redis флеш-сообщения теперь отображаются в компоненте location-name
@endphp

<div class="game-flash-messages">
    {{-- Специальные сообщения стана --}}
    <x-stun-flash-message />

    {{-- Сообщения об успехе --}}
    @if (session('success'))
        <div
            class="{{ $messageTypes['success']['bg'] }} {{ $messageTypes['success']['text'] }} p-1.5 mx-2 text-xs rounded border {{ $messageTypes['success']['border'] }} shadow-inner shadow-black/30 animate-fade-in">
            {!! session('success') !!}
        </div>
    @endif

    {{-- Сообщения об ошибке --}}
    @if (session('error'))
        <div
            class="{{ $messageTypes['error']['bg'] }} {{ $messageTypes['error']['text'] }} p-1.5 mx-2 text-xs rounded border {{ $messageTypes['error']['border'] }} shadow-inner shadow-black/30 animate-fade-in">
            {!! session('error') !!}
        </div>
    @endif

    {{-- Предупреждения --}}
    @if (session('warning'))
        <div
            class="{{ $messageTypes['warning']['bg'] }} {{ $messageTypes['warning']['text'] }} p-1.5 mx-2 text-xs rounded border {{ $messageTypes['warning']['border'] }} shadow-inner shadow-black/30 animate-fade-in">
            {!! session('warning') !!}
        </div>
    @endif

    {{-- Информационные сообщения --}}
    @if (session('info'))
        <div
            class="{{ $messageTypes['info']['bg'] }} {{ $messageTypes['info']['text'] }} p-1.5 mx-2 text-xs rounded border {{ $messageTypes['info']['border'] }} shadow-inner shadow-black/30 animate-fade-in">
            {!! session('info') !!}
        </div>
    @endif

    {{-- Сообщения о действиях с группой --}}
    @if (session('party_action'))
        <div
            class="{{ $messageTypes['party_action']['bg'] }} {{ $messageTypes['party_action']['text'] }} p-1.5 mx-2 text-xs rounded border {{ $messageTypes['party_action']['border'] }} shadow-inner shadow-black/30 animate-fade-in">
            {!! session('party_action') !!}
        </div>
    @endif

    {{-- Redis флеш-сообщения теперь отображаются в компоненте location-name --}}

    {{-- Специальное сообщение приветствия --}}
    @if (session('welcome_message'))
        <div
            class="bg-[#3b3a33] text-[#e5b769] p-2 rounded mb-2 mt-2 border border-[#a6925e] shadow-md animate-fade-in text-center">
            {{ session('welcome_message') }}
        </div>
    @endif

    {{-- Сообщение о блокировке доступа в подземелье --}}
    @if (session('dungeon_access_blocked'))
        @php
            $blockData = session('dungeon_access_blocked');
        @endphp
        <div
            class="bg-gradient-to-r from-[#6c4539] to-[#2a1b12] border border-[#6c4539] rounded-lg p-3 mx-2 my-2 animate-fade-in">
            <div class="flex items-center space-x-2 mb-2">
                <span class="text-[#f28b38] text-lg">⚠️</span>
                <span class="text-[#fceac4] font-semibold">Вы находитесь в подземелье</span>
            </div>
            <p class="text-[#fceac4] text-sm leading-relaxed mb-3">
                {{ $blockData['message'] }}
            </p>
            <p class="text-[#a6925e] text-xs mb-3">
                Если вы покидаете подземелье, то также покидаете группу.
            </p>

            {{-- Убрана кнопка "Покинуть подземелье" - теперь используется только окно подтверждения --}}
            <div class="flex justify-center">
                {{-- Кнопка "Закрыть уведомление" --}}
                <button onclick="this.parentElement.parentElement.style.display='none'"
                    class="bg-gradient-to-br from-[#3e5c48] to-[#243c2f] text-[#fceac4] font-semibold py-2 px-4 rounded border border-[#3e5c48] hover:from-[#4a6b54] hover:to-[#2d4738] transition-all duration-300 text-sm">
                    ❌ Закрыть уведомление
                </button>
            </div>
        </div>
    @endif

    {{-- Блок подтверждения выхода из подземелья перенесен в layout.game-flash-messages --}}
</div>

{{-- Стили для анимации --}}
<style>
    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .animate-fade-in {
        animation: fadeIn 0.3s ease-out forwards;
    }
</style>