<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['dungeons', 'userLevel' => 1, 'userGearScore' => 0]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['dungeons', 'userLevel' => 1, 'userGearScore' => 0]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>



<div class="space-y-2">
    <?php $__empty_1 = true; $__currentLoopData = $dungeons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $dungeon): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
        <?php if (isset($component)) { $__componentOriginal60be27578f07402c31008821efec6e88 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal60be27578f07402c31008821efec6e88 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dungeons.dungeon-card','data' => ['dungeon' => $dungeon,'userLevel' => $userLevel,'userGearScore' => $userGearScore]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dungeons.dungeon-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['dungeon' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($dungeon),'userLevel' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($userLevel),'userGearScore' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($userGearScore)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal60be27578f07402c31008821efec6e88)): ?>
<?php $attributes = $__attributesOriginal60be27578f07402c31008821efec6e88; ?>
<?php unset($__attributesOriginal60be27578f07402c31008821efec6e88); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal60be27578f07402c31008821efec6e88)): ?>
<?php $component = $__componentOriginal60be27578f07402c31008821efec6e88; ?>
<?php unset($__componentOriginal60be27578f07402c31008821efec6e88); ?>
<?php endif; ?>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
        <div class="bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] border-2 border-[#514b3c] rounded-lg p-6 text-center shadow-lg">
            <div class="text-[#9a9483] mb-3">
                <i class="text-4xl">🏰</i>
            </div>
            <h3 class="text-[#e5b769] font-semibold mb-2 text-lg">Подземелья недоступны</h3>
            <p class="text-[#b0a890] text-sm leading-relaxed">
                В данный момент нет доступных подземелий для исследования.<br>
                Возвращайтесь позже или повысьте свой уровень.
            </p>
        </div>
    <?php endif; ?>
</div><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/dungeons/dungeons-list.blade.php ENDPATH**/ ?>