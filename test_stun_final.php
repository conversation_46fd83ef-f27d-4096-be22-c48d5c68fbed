<?php

require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

$player = \App\Models\User::where('name', 'admin')->first();

if ($player) {
    echo "=== ФИНАЛЬНЫЙ ТЕСТ СИСТЕМЫ СТАНА ===" . PHP_EOL;
    echo "Player: {$player->name} (ID: {$player->id})" . PHP_EOL;
    echo "Current time: " . now()->format('Y-m-d H:i:s') . PHP_EOL;
    
    // Очищаем старые эффекты стана
    $player->activeEffects()->where('effect_type', 'stun')->delete();
    echo "Старые эффекты стана очищены" . PHP_EOL;
    
    // Создаем эффект стана на 15 секунд для быстрого тестирования
    $effect = \App\Models\ActiveEffect::create([
        'effect_type' => 'stun',
        'target_type' => 'player',
        'target_id' => $player->id,
        'source_type' => 'mob',
        'source_id' => 14, // Огр
        'skill_id' => 14,
        'duration' => 15,
        'ends_at' => now()->addSeconds(15),
        'power' => 1,
        'effect_data' => [
            'type' => 'stun',
            'template_id' => 1,
            'disable_skills' => true,
            'disable_movement' => true,
            'disable_actions' => true,
            'message' => '⚡ Вы оглушены тяжелым ударом и не можете действовать!'
        ]
    ]);
    
    echo "Эффект стана создан (ID: {$effect->id})" . PHP_EOL;
    echo "Длительность: 15 секунд (для быстрого тестирования)" . PHP_EOL;
    echo "Окончание: " . $effect->ends_at->format('Y-m-d H:i:s') . PHP_EOL;
    echo "Сообщение: {$effect->effect_data['message']}" . PHP_EOL;
    
    // Проверяем состояние стана
    $isStunned = $player->isStunned();
    echo "Игрок оглушен: " . ($isStunned ? 'ДА' : 'НЕТ') . PHP_EOL;
    
    echo PHP_EOL;
    echo "=== ПРОВЕРЬТЕ ИСПРАВЛЕНИЯ ===" . PHP_EOL;
    echo "✅ Flash сообщение в стиле обычных сообщений (красный фон, маленький размер)" . PHP_EOL;
    echo "✅ Компоненты затемнены и мерцают (НЕ желтым цветом)" . PHP_EOL;
    echo "✅ Глобальные стили страницы НЕ изменены" . PHP_EOL;
    echo "✅ При попытке действий - уведомление в том же стиле" . PHP_EOL;
    echo "✅ После окончания стана - адаптивное сообщение с кнопкой 'Обновить страницу'" . PHP_EOL;
    echo "✅ Никаких всплывающих JS окон" . PHP_EOL;
    echo PHP_EOL;
    echo "Откройте http://127.0.0.1:8000/ и войдите как admin/qwe123" . PHP_EOL;
    echo "Дождитесь окончания стана (15 секунд) и проверьте адаптивное сообщение" . PHP_EOL;
    
} else {
    echo "Пользователь 'admin' не найден!" . PHP_EOL;
}
