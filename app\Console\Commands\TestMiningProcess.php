<?php

namespace App\Console\Commands;

use App\Models\SpawnedResource;
use App\Models\Resource;
use App\Models\Location;
use App\Services\LocationResourceService;
use Carbon\Carbon;
use Illuminate\Console\Command;

class TestMiningProcess extends Command
{
    protected $signature = 'test:mining-process {--cleanup : Удалить тестовые ресурсы}';
    protected $description = 'Тестирование полного процесса добычи и респавна ресурсов';

    public function handle()
    {
        if ($this->option('cleanup')) {
            return $this->cleanup();
        }

        $this->info('⛏️ Тестирование полного процесса добычи и респавна ресурсов...');
        $this->newLine();

        // Шаг 1: Создаем тестовый ресурс
        $this->info('1️⃣ Создание тестового ресурса...');
        $testResource = $this->createTestResource();
        
        if (!$testResource) {
            $this->error('❌ Не удалось создать тестовый ресурс');
            return Command::FAILURE;
        }

        $this->line("   ✅ Создан ресурс ID: {$testResource->id}");
        $this->line("   📊 Начальная прочность: {$testResource->durability}/{$testResource->max_durability}");
        $this->line("   📊 Время респавна: {$testResource->respawn_time} минут");
        $this->line("   🎯 Шанс респавна: {$testResource->spawn_chance}%");
        $this->line("   📊 Активен: " . ($testResource->is_active ? 'Да' : 'Нет'));
        $this->newLine();

        // Шаг 2: Симулируем процесс добычи
        $this->info('2️⃣ Симуляция процесса добычи...');
        $service = app(LocationResourceService::class);
        $hitCount = 0;
        
        while ($testResource->durability > 0 && $hitCount < 20) { // Максимум 20 ударов для безопасности
            $hitCount++;
            $damage = rand(10, 20); // Случайный урон
            
            $this->line("   🔨 Удар #{$hitCount}: урон {$damage}");
            
            // Симулируем добычу
            $service->decreaseDurability($testResource, $damage);
            $testResource->refresh();
            
            $this->line("      💪 Прочность: {$testResource->durability}/{$testResource->max_durability}");
            $this->line("      📊 Активен: " . ($testResource->is_active ? 'Да' : 'Нет'));
            
            if ($testResource->durability <= 0) {
                $this->line("      ⏰ Время респавна: " . ($testResource->respawn_at ? $testResource->respawn_at->format('Y-m-d H:i:s') : 'null'));
                break;
            }
        }
        $this->newLine();

        // Шаг 3: Проверяем результат истощения
        $this->info('3️⃣ Проверка результата истощения...');
        if ($testResource->durability <= 0) {
            if (!$testResource->is_active && $testResource->respawn_at) {
                $this->line("   ✅ Ресурс правильно истощен:");
                $this->line("      - Неактивен: Да");
                $this->line("      - Время респавна установлено: {$testResource->respawn_at->format('Y-m-d H:i:s')}");
                $this->line("      - Текущее время: " . now()->format('Y-m-d H:i:s'));
                
                $minutesUntilRespawn = now()->diffInMinutes($testResource->respawn_at, false);
                $this->line("      - До респавна: " . abs($minutesUntilRespawn) . " минут");
            } else {
                $this->error("   ❌ Ресурс истощен неправильно:");
                $this->line("      - Активен: " . ($testResource->is_active ? 'Да' : 'Нет'));
                $this->line("      - Время респавна: " . ($testResource->respawn_at ? $testResource->respawn_at->format('Y-m-d H:i:s') : 'НЕ УСТАНОВЛЕНО'));
            }
        } else {
            $this->warn("   ⚠️ Ресурс не был полностью истощен за {$hitCount} ударов");
        }
        $this->newLine();

        // Шаг 4: Принудительно устанавливаем время респавна в прошлое для тестирования
        $this->info('4️⃣ Принудительная установка времени респавна в прошлое...');
        $testResource->respawn_at = Carbon::now()->subMinutes(1);
        $testResource->save();
        
        $this->line("   ✅ Время респавна установлено в прошлое: {$testResource->respawn_at->format('Y-m-d H:i:s')}");
        $this->line("   🕐 Текущее время: " . now()->format('Y-m-d H:i:s'));
        $this->newLine();

        // Шаг 5: Проверяем готовность к респавну
        $this->info('5️⃣ Проверка готовности к респавну...');
        $readyResources = SpawnedResource::where('is_active', false)
            ->where('respawn_at', '<=', Carbon::now())
            ->where('id', $testResource->id)
            ->count();
        
        $this->line("   📊 Ресурсов готовых к респавну: {$readyResources}");
        
        if ($readyResources > 0) {
            $this->line("   ✅ Ресурс готов к респавну");
        } else {
            $this->error("   ❌ Ресурс НЕ готов к респавну");
        }
        $this->newLine();

        // Шаг 6: Запускаем процесс респавна
        $this->info('6️⃣ Запуск процесса респавна...');
        $beforeActive = $testResource->is_active;
        $beforeDurability = $testResource->durability;
        
        $service->respawnResources();
        
        $testResource->refresh();
        $this->line("   📊 До респавна: Активен={$beforeActive}, Прочность={$beforeDurability}");
        $this->line("   📊 После респавна: Активен=" . ($testResource->is_active ? 'Да' : 'Нет') . ", Прочность={$testResource->durability}");
        $this->line("   ⏰ Время респавна: " . ($testResource->respawn_at ?? 'null'));
        $this->newLine();

        // Шаг 7: Результаты
        $this->info('7️⃣ Результаты тестирования:');
        
        $success = true;
        $issues = [];
        
        // Проверяем, что ресурс респавнился
        if (!$testResource->is_active) {
            $success = false;
            $issues[] = "Ресурс не активировался после респавна";
        }
        
        if ($testResource->durability <= 0) {
            $success = false;
            $issues[] = "Прочность не восстановилась";
        }
        
        if ($testResource->respawn_at !== null) {
            $success = false;
            $issues[] = "Время респавна не сброшено";
        }
        
        if ($success) {
            $this->line("   ✅ Тест ПРОЙДЕН: Полный цикл добычи и респавна работает правильно!");
        } else {
            $this->error("   ❌ Тест ПРОВАЛЕН: Найдены проблемы:");
            foreach ($issues as $issue) {
                $this->line("      - {$issue}");
            }
        }
        
        $this->newLine();
        $this->info('💡 Для очистки тестовых данных запустите: php artisan test:mining-process --cleanup');

        return $success ? Command::SUCCESS : Command::FAILURE;
    }

    private function createTestResource(): ?SpawnedResource
    {
        // Получаем первый доступный ресурс и локацию
        $resource = Resource::first();
        $location = Location::first();
        
        if (!$resource || !$location) {
            return null;
        }

        return SpawnedResource::create([
            'resource_id' => $resource->id,
            'location_id' => $location->id,
            'is_active' => true,
            'durability' => 50, // Небольшая прочность для быстрого тестирования
            'max_durability' => 100,
            'respawn_time' => 2, // 2 минуты для быстрого тестирования
            'spawn_chance' => 100, // 100% шанс для гарантированного респавна
            'respawn_at' => null
        ]);
    }

    private function cleanup(): int
    {
        $this->info('🧹 Очистка тестовых ресурсов...');
        
        // Удаляем тестовые ресурсы (с коротким временем респавна и небольшой прочностью)
        $deleted = SpawnedResource::where('respawn_time', 2)
            ->where('max_durability', 100)
            ->where('durability', '<=', 50)
            ->delete();
        
        $this->line("   ✅ Удалено тестовых ресурсов: {$deleted}");
        
        return Command::SUCCESS;
    }
}
