

<?php
    // Проверяем, заблокированы ли рудники для текущего пользователя
    $locationAccessService = app(\App\Services\LocationAccessService::class);
    $user = Auth::user();
    $isMinesBlocked = $user ? $locationAccessService->isLocationBlocked('Рудники', $user) : false;
?>

<div class="px-2 py-1">
    <div class="grid grid-cols-1 rounded-md overflow-hidden border border-[#a6925e] shadow-lg">
        
        <div class="bg-gradient-to-r from-[#3b3a33] to-[#4a4a3d] py-1 px-2 border-b border-[#a6925e]">
            <h3 class="text-center text-[#e5b769] font-semibold text-sm tracking-wider uppercase">Боевые локации
            </h3>
        </div>

        
        <?php if (isset($component)) { $__componentOriginal69a85482308c245528b866012ef9d6eb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal69a85482308c245528b866012ef9d6eb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.location-item','data' => ['route' => ''.e(route('battle.outposts.index')).'','icon' => 'assets/iconOutpost.png','title' => 'Аванпосты','isActive' => true]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.location-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['route' => ''.e(route('battle.outposts.index')).'','icon' => 'assets/iconOutpost.png','title' => 'Аванпосты','isActive' => true]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal69a85482308c245528b866012ef9d6eb)): ?>
<?php $attributes = $__attributesOriginal69a85482308c245528b866012ef9d6eb; ?>
<?php unset($__attributesOriginal69a85482308c245528b866012ef9d6eb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal69a85482308c245528b866012ef9d6eb)): ?>
<?php $component = $__componentOriginal69a85482308c245528b866012ef9d6eb; ?>
<?php unset($__componentOriginal69a85482308c245528b866012ef9d6eb); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal69a85482308c245528b866012ef9d6eb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal69a85482308c245528b866012ef9d6eb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.location-item','data' => ['route' => ''.e(route('battle.mines.index')).'','icon' => 'assets/iconMines.png','title' => 'Рудники','isActive' => !$isMinesBlocked,'isBlocked' => $isMinesBlocked]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.location-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['route' => ''.e(route('battle.mines.index')).'','icon' => 'assets/iconMines.png','title' => 'Рудники','isActive' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(!$isMinesBlocked),'isBlocked' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($isMinesBlocked)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal69a85482308c245528b866012ef9d6eb)): ?>
<?php $attributes = $__attributesOriginal69a85482308c245528b866012ef9d6eb; ?>
<?php unset($__attributesOriginal69a85482308c245528b866012ef9d6eb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal69a85482308c245528b866012ef9d6eb)): ?>
<?php $component = $__componentOriginal69a85482308c245528b866012ef9d6eb; ?>
<?php unset($__componentOriginal69a85482308c245528b866012ef9d6eb); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal69a85482308c245528b866012ef9d6eb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal69a85482308c245528b866012ef9d6eb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.location-item','data' => ['route' => ''.e(route('dungeons.index')).'','icon' => 'assets/iconDungeon.png','title' => 'Подземелья','isActive' => true]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.location-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['route' => ''.e(route('dungeons.index')).'','icon' => 'assets/iconDungeon.png','title' => 'Подземелья','isActive' => true]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal69a85482308c245528b866012ef9d6eb)): ?>
<?php $attributes = $__attributesOriginal69a85482308c245528b866012ef9d6eb; ?>
<?php unset($__attributesOriginal69a85482308c245528b866012ef9d6eb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal69a85482308c245528b866012ef9d6eb)): ?>
<?php $component = $__componentOriginal69a85482308c245528b866012ef9d6eb; ?>
<?php unset($__componentOriginal69a85482308c245528b866012ef9d6eb); ?>
<?php endif; ?>

        
        <div class="px-2 py-0 bg-gradient-to-r from-[#292722] to-[#38352c]">
            <div class="h-px bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-50"></div>
        </div>

        
        <?php if (isset($component)) { $__componentOriginal69a85482308c245528b866012ef9d6eb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal69a85482308c245528b866012ef9d6eb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.location-item','data' => ['route' => '#','icon' => 'assets/iconTrial.png','title' => 'Испытание','isActive' => false,'badge' => 'В разработке','onClick' => 'showMobileTooltip(\'trial-tooltip\')']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.location-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['route' => '#','icon' => 'assets/iconTrial.png','title' => 'Испытание','isActive' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false),'badge' => 'В разработке','onClick' => 'showMobileTooltip(\'trial-tooltip\')']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal69a85482308c245528b866012ef9d6eb)): ?>
<?php $attributes = $__attributesOriginal69a85482308c245528b866012ef9d6eb; ?>
<?php unset($__attributesOriginal69a85482308c245528b866012ef9d6eb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal69a85482308c245528b866012ef9d6eb)): ?>
<?php $component = $__componentOriginal69a85482308c245528b866012ef9d6eb; ?>
<?php unset($__componentOriginal69a85482308c245528b866012ef9d6eb); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal69a85482308c245528b866012ef9d6eb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal69a85482308c245528b866012ef9d6eb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.location-item','data' => ['route' => '#','icon' => 'assets/iconDominions.png','title' => 'Доминионы','isActive' => false,'badge' => 'В разработке','onClick' => 'showMobileTooltip(\'dominions-tooltip\')']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.location-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['route' => '#','icon' => 'assets/iconDominions.png','title' => 'Доминионы','isActive' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false),'badge' => 'В разработке','onClick' => 'showMobileTooltip(\'dominions-tooltip\')']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal69a85482308c245528b866012ef9d6eb)): ?>
<?php $attributes = $__attributesOriginal69a85482308c245528b866012ef9d6eb; ?>
<?php unset($__attributesOriginal69a85482308c245528b866012ef9d6eb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal69a85482308c245528b866012ef9d6eb)): ?>
<?php $component = $__componentOriginal69a85482308c245528b866012ef9d6eb; ?>
<?php unset($__componentOriginal69a85482308c245528b866012ef9d6eb); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal69a85482308c245528b866012ef9d6eb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal69a85482308c245528b866012ef9d6eb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.location-item','data' => ['route' => '#','icon' => 'assets/iconTemporary.png','title' => 'Временные событие','isActive' => false,'badge' => 'В разработке','onClick' => 'showMobileTooltip(\'events-tooltip\')']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.location-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['route' => '#','icon' => 'assets/iconTemporary.png','title' => 'Временные событие','isActive' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false),'badge' => 'В разработке','onClick' => 'showMobileTooltip(\'events-tooltip\')']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal69a85482308c245528b866012ef9d6eb)): ?>
<?php $attributes = $__attributesOriginal69a85482308c245528b866012ef9d6eb; ?>
<?php unset($__attributesOriginal69a85482308c245528b866012ef9d6eb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal69a85482308c245528b866012ef9d6eb)): ?>
<?php $component = $__componentOriginal69a85482308c245528b866012ef9d6eb; ?>
<?php unset($__componentOriginal69a85482308c245528b866012ef9d6eb); ?>
<?php endif; ?>
    </div>
</div><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/battle/location-menu.blade.php ENDPATH**/ ?>