<?php $__env->startSection('title', 'Редактирование ресурса'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4">
    <h1 class="text-2xl font-bold text-[#e5b769] mb-6">Редактирование ресурса</h1>

    <div class="bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] border-2 border-[#514b3c] rounded-lg shadow-lg">
        <div class="px-6 py-4 border-b border-[#514b3c]">
            <h6 class="font-bold text-[#e5b769]">Форма редактирования ресурса</h6>
        </div>
        <div class="p-6">
            <form action="<?php echo e(route('admin.location-resources.update', $spawnedResource->id)); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>

                <div class="mb-4">
                    <label for="location_id" class="block text-[#e5b769] mb-1">Локация:</label>
                    <select name="location_id" id="location_id" 
                        class="w-full bg-[#2a2721] border border-[#514b3c] rounded px-3 py-2 text-[#d9d3b8] focus:outline-none focus:border-[#a6925e] <?php $__errorArgs = ['location_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                        required>
                        <option value="">Выберите локацию</option>
                        <?php $__currentLoopData = $locations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $location): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($location->id); ?>" <?php echo e(old('location_id', $spawnedResource->location_id) == $location->id ? 'selected' : ''); ?>>
                                <?php echo e($location->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                    <?php $__errorArgs = ['location_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="text-red-500 text-sm mt-1"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div class="mb-4">
                    <label for="resource_id" class="block text-[#e5b769] mb-2">
                        <i class="fas fa-gem mr-1"></i> Тип ресурса:
                    </label>
                    
                    
                    <input type="hidden" name="resource_id" id="resource_id_hidden" value="<?php echo e(old('resource_id', $spawnedResource->resource_id)); ?>" required>
                    
                    
                    <div class="relative">
                        <button type="button" id="resource_selector_button"
                            class="w-full bg-[#2a2721] border border-[#514b3c] rounded px-3 py-2 text-[#d9d3b8] focus:outline-none focus:border-[#a6925e] <?php $__errorArgs = ['resource_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?> flex items-center justify-between">
                            <div class="flex items-center" id="selected_resource_display">
                                <span class="text-[#998d66]">-- Выберите тип ресурса --</span>
                            </div>
                            <i class="fas fa-chevron-down text-[#998d66]"></i>
                        </button>
                        
                        
                        <div id="resource_dropdown" class="absolute top-full left-0 right-0 mt-1 bg-[#2a2721] border border-[#514b3c] rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto hidden">
                            <div class="p-2">
                                <?php $__currentLoopData = $resources; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $resource): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="resource-option flex items-center p-3 hover:bg-[#3b3629] cursor-pointer rounded transition-colors" 
                                         data-resource-id="<?php echo e($resource->id); ?>"
                                         data-resource-name="<?php echo e($resource->name); ?>"
                                         data-resource-type="<?php echo e($resource->type ?? ''); ?>"
                                         data-resource-icon="<?php echo e($resource->icon_path); ?>">
                                        <div class="w-10 h-10 bg-[#1a1814] rounded border border-[#3b3629] flex items-center justify-center mr-3 flex-shrink-0">
                                            <?php if($resource->icon): ?>
                                                <img src="<?php echo e($resource->icon_path); ?>" alt="<?php echo e($resource->name); ?>" class="w-8 h-8 object-cover rounded">
                                            <?php else: ?>
                                                <span class="text-[#c1a96e] text-lg font-bold"><?php echo e(substr($resource->name, 0, 1)); ?></span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="flex-1">
                                            <div class="text-[#d9d3b8] font-medium"><?php echo e($resource->name); ?></div>
                                            <?php if($resource->type): ?>
                                                <div class="text-[#998d66] text-sm"><?php echo e($resource->getTypeTranslation()); ?></div>
                                            <?php endif; ?>
                                        </div>
                                        <?php if($resource->rarity): ?>
                                            <div class="ml-2">
                                                <span class="inline-block w-3 h-3 rounded-full" style="background-color: <?php echo e($resource->rarity_color); ?>"></span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>
                    
                    <?php $__errorArgs = ['resource_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="text-red-500 text-sm mt-1"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="durability" class="block text-[#e5b769] mb-1">Текущая прочность:</label>
                        <input type="number" name="durability" id="durability" 
                            class="w-full bg-[#2a2721] border border-[#514b3c] rounded px-3 py-2 text-[#d9d3b8] focus:outline-none focus:border-[#a6925e] <?php $__errorArgs = ['durability'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                            value="<?php echo e(old('durability', $spawnedResource->durability)); ?>" min="0" required>
                        <?php $__errorArgs = ['durability'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="text-red-500 text-sm mt-1"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div>
                        <label for="max_durability" class="block text-[#e5b769] mb-1">Максимальная прочность:</label>
                        <input type="number" name="max_durability" id="max_durability" 
                            class="w-full bg-[#2a2721] border border-[#514b3c] rounded px-3 py-2 text-[#d9d3b8] focus:outline-none focus:border-[#a6925e] <?php $__errorArgs = ['max_durability'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                            value="<?php echo e(old('max_durability', $spawnedResource->max_durability)); ?>" min="1" required>
                        <?php $__errorArgs = ['max_durability'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="text-red-500 text-sm mt-1"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>

                <div class="mb-4">
                    <div class="flex items-center">
                        <input type="checkbox" id="is_active" name="is_active" value="1" 
                            class="w-4 h-4 bg-[#2a2721] border border-[#514b3c] rounded focus:ring-[#a6925e] text-[#a6925e]" 
                            <?php echo e(old('is_active', $spawnedResource->is_active) ? 'checked' : ''); ?>>
                        <label class="ml-2 text-[#d9d3b8]" for="is_active">Активен</label>
                    </div>
                    <?php $__errorArgs = ['is_active'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="text-red-500 text-sm mt-1"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="respawn_time" class="block text-[#e5b769] mb-1">Время респавна (в минутах):</label>
                        <input type="number" name="respawn_time" id="respawn_time"
                            class="w-full bg-[#2a2721] border border-[#514b3c] rounded px-3 py-2 text-[#d9d3b8] focus:outline-none focus:border-[#a6925e] <?php $__errorArgs = ['respawn_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                            value="<?php echo e(old('respawn_time', $spawnedResource->respawn_time ?? 60)); ?>" min="1">
                        <?php $__errorArgs = ['respawn_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="text-red-500 text-sm mt-1"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div>
                        <label for="spawn_chance" class="block text-[#e5b769] mb-1">Шанс респавна (%):</label>
                        <input type="number" name="spawn_chance" id="spawn_chance"
                            class="w-full bg-[#2a2721] border border-[#514b3c] rounded px-3 py-2 text-[#d9d3b8] focus:outline-none focus:border-[#a6925e] <?php $__errorArgs = ['spawn_chance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                            value="<?php echo e(old('spawn_chance', $spawnedResource->spawn_chance ?? 100)); ?>" min="1" max="100">
                        <?php $__errorArgs = ['spawn_chance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="text-red-500 text-sm mt-1"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>

                <div class="mb-4">
                    <label for="respawn_at" class="block text-[#e5b769] mb-1">Точное время следующего респауна:</label>
                    <input type="datetime-local" name="respawn_at" id="respawn_at" 
                        class="w-full bg-[#2a2721] border border-[#514b3c] rounded px-3 py-2 text-[#d9d3b8] focus:outline-none focus:border-[#a6925e] <?php $__errorArgs = ['respawn_at'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                        value="<?php echo e(old('respawn_at', $spawnedResource->respawn_at ? $spawnedResource->respawn_at->format('Y-m-d\\TH:i') : '')); ?>">
                    <small class="text-[#847f72] text-sm mt-1 block">Оставьте пустым, если ресурс не должен респауниться по точному таймеру</small>
                    <?php $__errorArgs = ['respawn_at'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="text-red-500 text-sm mt-1"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div class="flex justify-end space-x-3 mt-6">
                    <a href="<?php echo e(route('admin.location-resources.index')); ?>" 
                       class="px-4 py-2 bg-[#3a3631] text-[#9a9483] rounded hover:bg-[#4a4641] transition duration-300">
                        Отмена
                    </a>
                    <button type="submit" 
                            class="px-4 py-2 bg-[#5c4f2a] text-[#e5b769] rounded hover:bg-[#6c5f3a] transition duration-300">
                        Сохранить
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Получаем элементы формы
        const respawnTimeInput = document.getElementById('respawn_time');
        const respawnAtInput = document.getElementById('respawn_at');
        const isActiveCheckbox = document.getElementById('is_active');
        const durabilityInput = document.getElementById('durability');
        
        // Элементы для селектора ресурсов
        const resourceSelectorButton = document.getElementById('resource_selector_button');
        const resourceDropdown = document.getElementById('resource_dropdown');
        const resourceIdHidden = document.getElementById('resource_id_hidden');
        const selectedResourceDisplay = document.getElementById('selected_resource_display');
        const resourceOptions = document.querySelectorAll('.resource-option');

        // Инициализируем выбранный ресурс при загрузке страницы
        const currentResourceId = resourceIdHidden.value;
        if (currentResourceId) {
            const selectedOption = document.querySelector(`[data-resource-id="${currentResourceId}"]`);
            if (selectedOption) {
                updateSelectedResourceDisplay(selectedOption);
            }
        }

        // Обработчик клика по кнопке селектора ресурсов
        if (resourceSelectorButton) {
            resourceSelectorButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                resourceDropdown.classList.toggle('hidden');
            });
        }

        // Обработчики кликов по опциям ресурсов
        resourceOptions.forEach(option => {
            option.addEventListener('click', function() {
                const resourceId = this.getAttribute('data-resource-id');
                const resourceName = this.getAttribute('data-resource-name');
                const resourceType = this.getAttribute('data-resource-type');
                const resourceIcon = this.getAttribute('data-resource-icon');

                // Обновляем скрытое поле
                resourceIdHidden.value = resourceId;

                // Обновляем отображение выбранного ресурса
                updateSelectedResourceDisplay(this);

                // Скрываем выпадающий список
                resourceDropdown.classList.add('hidden');
            });
        });

        // Функция для обновления отображения выбранного ресурса
        function updateSelectedResourceDisplay(option) {
            const resourceName = option.getAttribute('data-resource-name');
            const resourceType = option.getAttribute('data-resource-type');
            const resourceIcon = option.getAttribute('data-resource-icon');
            
            const iconImg = option.querySelector('img');
            let iconHtml = '';
            
            if (iconImg) {
                iconHtml = `<div class="w-8 h-8 bg-[#1a1814] rounded border border-[#3b3629] flex items-center justify-center mr-3 flex-shrink-0">
                               <img src="${resourceIcon}" alt="${resourceName}" class="w-6 h-6 object-cover rounded">
                           </div>`;
            } else {
                iconHtml = `<div class="w-8 h-8 bg-[#1a1814] rounded border border-[#3b3629] flex items-center justify-center mr-3 flex-shrink-0">
                               <span class="text-[#c1a96e] text-sm font-bold">${resourceName.charAt(0)}</span>
                           </div>`;
            }
            
            const typeDisplay = resourceType ? ` (${resourceType})` : '';
            
            selectedResourceDisplay.innerHTML = `
                ${iconHtml}
                <div>
                    <div class="text-[#d9d3b8] font-medium">${resourceName}${typeDisplay}</div>
                </div>
            `;
        }

        // Закрытие выпадающего списка при клике вне его
        document.addEventListener('click', function(e) {
            if (!resourceSelectorButton.contains(e.target) && !resourceDropdown.contains(e.target)) {
                resourceDropdown.classList.add('hidden');
            }
        });
        
        // Функция для обновления времени респауна
        function updateRespawnAt() {
            // Если ресурс активен или прочность больше 0, не обновляем автоматически
            if (isActiveCheckbox.checked || parseInt(durabilityInput.value) > 0) {
                return;
            }
            
            // Получаем текущее время
            const now = new Date();
            // Добавляем указанное количество минут
            const respawnTime = parseInt(respawnTimeInput.value);
            const respawnAt = new Date(now.getTime() + respawnTime * 60000);
            
            // Форматируем дату/время для поля datetime-local
            const year = respawnAt.getFullYear();
            const month = String(respawnAt.getMonth() + 1).padStart(2, '0');
            const day = String(respawnAt.getDate()).padStart(2, '0');
            const hours = String(respawnAt.getHours()).padStart(2, '0');
            const minutes = String(respawnAt.getMinutes()).padStart(2, '0');
            
            // Устанавливаем значение в поле respawn_at
            respawnAtInput.value = `${year}-${month}-${day}T${hours}:${minutes}`;
        }
        
        // Обработчики событий для автоматического обновления
        if (respawnTimeInput && respawnAtInput && isActiveCheckbox && durabilityInput) {
            respawnTimeInput.addEventListener('input', updateRespawnAt);
            isActiveCheckbox.addEventListener('change', function() {
                if (!this.checked && parseInt(durabilityInput.value) <= 0) {
                    updateRespawnAt();
                } else if (this.checked) {
                    // Если ресурс активен, очищаем поле респауна
                    respawnAtInput.value = '';
                }
            });
            durabilityInput.addEventListener('input', function() {
                if (!isActiveCheckbox.checked && parseInt(this.value) <= 0) {
                    updateRespawnAt();
                }
            });
        }
    });
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/admin/location-resources/edit.blade.php ENDPATH**/ ?>