<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'route' => '#',
    'icon' => '',
    'title' => '',
    'isActive' => true,
    'isBlocked' => false,
    'badge' => null,
    'tooltipId' => null,
    'onClick' => null
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'route' => '#',
    'icon' => '',
    'title' => '',
    'isActive' => true,
    'isBlocked' => false,
    'badge' => null,
    'tooltipId' => null,
    'onClick' => null
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>



<?php if($isBlocked): ?>
    
    <div class="relative flex items-center text-left text-[#a09a8a] pl-0 pr-2 bg-gradient-to-r from-[#292722] to-[#38352c] border-b border-[#a6925e] overflow-hidden">
        <span
            class="w-10 h-10 flex items-center justify-center mr-0 text-[#a09a8a] bg-gradient-to-br from-[#38352c] to-[#292722] border-r border-[#a6925e] relative">
            <img src="<?php echo e(asset($icon)); ?>" alt="<?php echo e($title); ?>"
                class="w-8 h-8 filter grayscale opacity-50">
            
           
        </span>
        <span class="font-medium tracking-wide drop-shadow-[0_1px_1px_rgba(0,0,0,0.8)] ml-2 opacity-75"><?php echo e($title); ?></span>

        
        <span class="ml-auto flex items-center space-x-1">
            <div class="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
            <span class="text-orange-400 text-xs font-medium">На доработке</span>
        </span>

        
        <div class="absolute inset-0 bg-black bg-opacity-20 pointer-events-none"></div>

        
        <div class="absolute inset-0 opacity-10 pointer-events-none"
             style="background-image: repeating-linear-gradient(
                 45deg,
                 transparent,
                 transparent 8px,
                 rgba(255, 165, 0, 0.3) 8px,
                 rgba(255, 165, 0, 0.3) 16px
             );">
        </div>
    </div>
<?php elseif($isActive): ?>
    <a href="<?php echo e($route); ?>"
        class="flex items-center text-left text-[#e0d0a0] pl-0 pr-2 bg-gradient-to-r from-[#292722] to-[#38352c] border-b border-[#a6925e] transition duration-300 hover:from-[#38352c] hover:to-[#4a452c] hover:text-[#e5b769]">
        <span
            class="w-10 h-10 flex items-center justify-center mr-0 text-[#e5b769] bg-gradient-to-br from-[#38352c] to-[#292722] border-r border-[#a6925e]">
            <img src="<?php echo e(asset($icon)); ?>" alt="<?php echo e($title); ?>"
                class="w-8 h-8 filter drop-shadow-[0_0_2px_rgba(229,183,105,0.5)]">
        </span>
        <span
            class="font-medium tracking-wide drop-shadow-[0_1px_1px_rgba(0,0,0,0.8)] ml-2"><?php echo e($title); ?></span>
    </a>
<?php else: ?>
    <div class="flex items-center text-left text-[#a09a8a] pl-0 pr-2 bg-gradient-to-r from-[#292722] to-[#38352c] border-b border-[#a6925e] relative group md:cursor-default cursor-pointer"
        <?php if($onClick): ?> onclick="<?php echo e($onClick); ?>" <?php endif; ?>>
        <span
            class="w-10 h-10 flex items-center justify-center mr-0 text-[#a09a8a] bg-gradient-to-br from-[#38352c] to-[#292722] border-r border-[#a6925e]">
            <img src="<?php echo e(asset($icon)); ?>" alt="<?php echo e($title); ?>"
                class="w-8 h-8 filter grayscale opacity-70">
        </span>
        <span
            class="font-medium tracking-wide drop-shadow-[0_1px_1px_rgba(0,0,0,0.8)] ml-2"><?php echo e($title); ?></span>
        
        <?php if($badge): ?>
            <span class="ml-auto">
                <span
                    class="inline-flex items-center px-1.5 py-0.5 rounded-sm bg-[#3b3a33] text-[#a09a8a] text-xs border border-[#a6925e] relative overflow-hidden">
                    <span class="relative z-10"><?php echo e($badge); ?></span>
                    <span
                        class="absolute inset-0 bg-gradient-to-r from-[#3b3a33] via-[#4a4a3d] to-[#3b3a33] opacity-50 animate-pulse"></span>
                </span>
            </span>
        <?php endif; ?>
    </div>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/battle/location-item.blade.php ENDPATH**/ ?>