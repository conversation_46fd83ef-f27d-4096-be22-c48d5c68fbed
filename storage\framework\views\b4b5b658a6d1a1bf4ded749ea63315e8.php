<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['navigationItems' => []]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['navigationItems' => []]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>



<div class="w-full navigation-container" style="margin: 0; padding: 0; line-height: 0; font-size: 0;">
    <?php $__currentLoopData = $navigationItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <a href="<?php echo e(route($item['route'])); ?>"
           class="navigation-button-link block w-full max-w-full"
           style="margin: 0; padding: 0; line-height: 0; font-size: 0; display: block;"
           data-route="<?php echo e($item['route']); ?>"
           data-title="<?php echo e($item['title']); ?>">

            
            <?php
                // Определяем изображение в зависимости от типа кнопки
                $buttonImage = match ($item['title']) {
                    'Сражение' => 'assets/UI/button-link-battle.png',
                    'Торговля' => 'assets/UI/button-link-shop.png',
                    'Таверна' => 'assets/UI/button-link-tavern.png',
                    'Профессии' => 'assets/UI/button-link-proff.png',
                    'Фермерство' => 'assets/UI/button-link-farm.png',
                    'Площадь' => 'assets/UI/button-link-square.png',
                    'Гильдии' => 'assets/UI/button-link-guild.png',
                    'Топ игроков' => 'assets/UI/top-players-nav.png',
                    'Топ тестировщиков' => 'assets/UI/top-test-nav.png',
                    default => 'assets/UI/linkUI.png'
                };
            ?>
            <img src="<?php echo e(asset($buttonImage)); ?>"
                 alt="<?php echo e($item['title']); ?>"
                 class="navigation-button-image w-full block"
                 style="margin: 0; padding: 0; display: block; vertical-align: top; line-height: 0; font-size: 0; border: none; outline: none;"
                 loading="eager">

        </a>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</div><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/layout/main-navigation.blade.php ENDPATH**/ ?>