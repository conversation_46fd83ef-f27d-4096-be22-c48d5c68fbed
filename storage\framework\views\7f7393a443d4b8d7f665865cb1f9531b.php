<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['route' => 'donations.index', 'title' => 'Поддержать проект', 'subtitle' => '💎 Помочь развитию игры']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['route' => 'donations.index', 'title' => 'Поддержать проект', 'subtitle' => '💎 Помочь развитию игры']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>



<a href="<?php echo e(route($route)); ?>"
    class="group relative flex items-center p-4 bg-gradient-to-r from-[#8B4513] via-[#A0522D] to-[#CD853F] border-2 border-[#DAA520] rounded-lg shadow-2xl hover:shadow-[0_0_30px_rgba(218,165,32,0.4)] transition-all duration-500 hover:-translate-y-2 hover:scale-105 overflow-hidden donation-button">

    
    <div class="absolute inset-0 bg-gradient-to-r from-[#FFD700] via-[#FFA500] to-[#FF8C00] opacity-0 group-hover:opacity-20 transition-opacity duration-500"></div>

    
    <div class="absolute top-1 left-2 w-1 h-1 bg-[#FFD700] rounded-full animate-pulse"></div>
    <div class="absolute top-3 right-4 w-1.5 h-1.5 bg-[#FFA500] rounded-full animate-pulse animation-delay-300"></div>
    <div class="absolute bottom-2 left-6 w-1 h-1 bg-[#FFD700] rounded-full animate-pulse animation-delay-700"></div>

    
    <div class="relative z-10 w-12 h-12 flex items-center justify-center bg-gradient-to-br from-[#FFD700] to-[#DAA520] rounded-full mr-4 shadow-lg group-hover:scale-110 group-hover:rotate-12 transition-all duration-500 border-2 border-[#FFA500]">
        <svg class="w-6 h-6 text-[#8B4513]" fill="currentColor" viewBox="0 0 20 20">
            <path d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"></path>
        </svg>
        
        <div class="absolute inset-0 bg-gradient-to-br from-[#FFFF00] to-transparent opacity-30 rounded-full group-hover:opacity-50 transition-opacity duration-500"></div>
    </div>

    
    <div class="relative z-10 flex flex-col">
        <span class="text-[#FFD700] font-bold text-lg group-hover:text-[#FFFF00] transition-colors duration-300 drop-shadow-lg">
            <?php echo e($title); ?>

        </span>
        <span class="text-[#DEB887] text-sm group-hover:text-[#F5DEB3] transition-colors duration-300">
            <?php echo e($subtitle); ?>

        </span>
    </div>

    
    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-[#FFD700] to-transparent opacity-0 group-hover:opacity-30 transform -translate-x-full group-hover:translate-x-full transition-all duration-1000 skew-x-12"></div>

    
    <div class="absolute inset-0 border-2 border-[#FFD700] rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500 animate-pulse"></div>
</a>
<?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/layout/donation-button.blade.php ENDPATH**/ ?>