
<!DOCTYPE html>
<html lang="ru">
<?php use Illuminate\Support\Facades\Auth; ?>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>Подземелья - Echoes of Eternity</title>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/css/dungeons/compact-rewards-carousel.css', 'resources/js/app.js', 'resources/js/dungeons/dungeon-rewards-carousel.js', 'resources/js/dungeons/horizontal-rewards-scroll.js', 'resources/js/global/csrf.js', 'resources/js/layout/footer-counters.js', 'resources/js/layout/server-time.js']); ?>
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif min-h-screen flex flex-col">
    
    <div class="container max-w-md mx-auto px-1 py-0 bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] border-2 border-[#a6925e] rounded-lg flex-grow"
        style="background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.0))">

        
        <?php if (isset($component)) { $__componentOriginalee64098a97531effaa5ca39da6b3f2bd = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee64098a97531effaa5ca39da6b3f2bd = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.hp-mp-bar','data' => ['actualResources' => $actualResources,'userProfile' => $userProfile]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.hp-mp-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['actualResources' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($actualResources),'userProfile' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($userProfile)]); ?>
            
            <?php if (isset($component)) { $__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.notifications-bar','data' => ['hasUnreadMessages' => false,'unreadMessagesCount' => 0,'hasBrokenItems' => false,'brokenItemsCount' => 0]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.notifications-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['hasUnreadMessages' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false),'unreadMessagesCount' => 0,'hasBrokenItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false),'brokenItemsCount' => 0]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb)): ?>
<?php $attributes = $__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb; ?>
<?php unset($__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb)): ?>
<?php $component = $__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb; ?>
<?php unset($__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb); ?>
<?php endif; ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee64098a97531effaa5ca39da6b3f2bd)): ?>
<?php $attributes = $__attributesOriginalee64098a97531effaa5ca39da6b3f2bd; ?>
<?php unset($__attributesOriginalee64098a97531effaa5ca39da6b3f2bd); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee64098a97531effaa5ca39da6b3f2bd)): ?>
<?php $component = $__componentOriginalee64098a97531effaa5ca39da6b3f2bd; ?>
<?php unset($__componentOriginalee64098a97531effaa5ca39da6b3f2bd); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal360d002b1b676b6f84d43220f22129e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal360d002b1b676b6f84d43220f22129e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.breadcrumbs','data' => ['breadcrumbs' => $breadcrumbs]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('breadcrumbs'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['breadcrumbs' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($breadcrumbs)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal360d002b1b676b6f84d43220f22129e2)): ?>
<?php $attributes = $__attributesOriginal360d002b1b676b6f84d43220f22129e2; ?>
<?php unset($__attributesOriginal360d002b1b676b6f84d43220f22129e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal360d002b1b676b6f84d43220f22129e2)): ?>
<?php $component = $__componentOriginal360d002b1b676b6f84d43220f22129e2; ?>
<?php unset($__componentOriginal360d002b1b676b6f84d43220f22129e2); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginalc249f68c23236e1f9e731ca34e4bd461 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc249f68c23236e1f9e731ca34e4bd461 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.location-name','data' => ['title' => 'Подземелья']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.location-name'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Подземелья']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc249f68c23236e1f9e731ca34e4bd461)): ?>
<?php $attributes = $__attributesOriginalc249f68c23236e1f9e731ca34e4bd461; ?>
<?php unset($__attributesOriginalc249f68c23236e1f9e731ca34e4bd461); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc249f68c23236e1f9e731ca34e4bd461)): ?>
<?php $component = $__componentOriginalc249f68c23236e1f9e731ca34e4bd461; ?>
<?php unset($__componentOriginalc249f68c23236e1f9e731ca34e4bd461); ?>
<?php endif; ?>

        
        <div class="bg-[#211f1a] px-2.5 shadow-inner">
            
            <?php if (isset($component)) { $__componentOriginal222696ac328fc8d428d5fdaab8ce22b2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal222696ac328fc8d428d5fdaab8ce22b2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dungeons.dungeons-list','data' => ['dungeons' => $dungeons,'userLevel' => $userLevel,'userGearScore' => $userGearScore]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dungeons.dungeons-list'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['dungeons' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($dungeons),'userLevel' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($userLevel),'userGearScore' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($userGearScore)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal222696ac328fc8d428d5fdaab8ce22b2)): ?>
<?php $attributes = $__attributesOriginal222696ac328fc8d428d5fdaab8ce22b2; ?>
<?php unset($__attributesOriginal222696ac328fc8d428d5fdaab8ce22b2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal222696ac328fc8d428d5fdaab8ce22b2)): ?>
<?php $component = $__componentOriginal222696ac328fc8d428d5fdaab8ce22b2; ?>
<?php unset($__componentOriginal222696ac328fc8d428d5fdaab8ce22b2); ?>
<?php endif; ?>

            
            <?php if($dungeons->hasPages()): ?>
                <div class="mt-6 mb-4 flex justify-center items-center">
                    
                    <div class="relative">
                        
                        <div class="relative bg-gradient-to-b from-[#312e25] to-[#1a1814] rounded-lg px-5 py-3
                                       border border-[#a6925e] shadow-md transform-gpu
                                       before:absolute before:inset-0 before:bg-gradient-to-t before:from-transparent before:to-[rgba(229,183,105,0.10)] before:rounded-lg
                                       before:pointer-events-none before:opacity-80
                                       after:absolute after:inset-0 after:rounded-lg after:shadow-[inset_0_1px_2px_rgba(255,255,255,0.15),0_3px_8px_rgba(0,0,0,0.4)]
                                       after:pointer-events-none">

                            
                            <div
                                class="absolute top-[6px] left-3 right-3 h-[1px] bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-60 pointer-events-none">
                            </div>
                            <div
                                class="absolute bottom-[6px] left-3 right-3 h-[1px] bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-60 pointer-events-none">
                            </div>

                            <div class="flex items-center justify-center">
                                
                                <?php if($dungeons->onFirstPage()): ?>
                                    <button disabled
                                        class="w-10 h-10 flex items-center justify-center rounded border border-[#3d3a32] bg-[#1a1814] text-[#514b3c] cursor-not-allowed opacity-70 mr-2 pointer-events-none">
                                        <span class="text-lg font-medium">←</span>
                                    </button>
                                <?php else: ?>
                                    <a href="<?php echo e($dungeons->previousPageUrl()); ?>"
                                        class="w-10 h-10 flex items-center justify-center rounded border border-[#514b3c] bg-[#252117] hover:bg-[#2d2820] hover:border-[#a6925e] transition-all duration-300 text-[#a6925e] mr-2 hover:text-[#e5b769] pointer-events-auto">
                                        <span class="text-lg font-medium">←</span>
                                    </a>
                                <?php endif; ?>

                                
                                <div class="flex items-center justify-center">
                                    <?php $__currentLoopData = $dungeons->getUrlRange(1, $dungeons->lastPage()); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $page => $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <a href="<?php echo e($url); ?>" class="relative w-10 h-10 flex items-center justify-center mx-1 rounded <?php echo e($page == $dungeons->currentPage()
                                        ? 'border border-[#e5b769] bg-[#2d2820] text-[#e5b769] font-medium shadow-[inset_0_1px_3px_rgba(0,0,0,0.3)] pointer-events-auto'
                                        : 'border border-[#514b3c] bg-[#252117] text-[#a6925e] hover:bg-[#2d2820] hover:border-[#a6925e] hover:text-[#e5b769] pointer-events-auto'); ?>

                                                                        transition-colors duration-300">
                                                                <?php if($page == $dungeons->currentPage()): ?>
                                                                    
                                                                    <div
                                                                        class="absolute top-0 left-0 w-[5px] h-[5px] border-t border-l border-[#e5b769] opacity-80 pointer-events-none">
                                                                    </div>
                                                                    <div
                                                                        class="absolute top-0 right-0 w-[5px] h-[5px] border-t border-r border-[#e5b769] opacity-80 pointer-events-none">
                                                                    </div>
                                                                    <div
                                                                        class="absolute bottom-0 left-0 w-[5px] h-[5px] border-b border-l border-[#e5b769] opacity-80 pointer-events-none">
                                                                    </div>
                                                                    <div
                                                                        class="absolute bottom-0 right-0 w-[5px] h-[5px] border-b border-r border-[#e5b769] opacity-80 pointer-events-none">
                                                                    </div>
                                                                    <span class="text-lg"
                                                                        style="text-shadow: 0 0 4px rgba(229, 183, 105, 0.5);"><?php echo e($page); ?></span>
                                                                <?php else: ?>
                                                                    <span class="text-lg"><?php echo e($page); ?></span>
                                                                <?php endif; ?>
                                                            </a>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>

                                
                                <?php if($dungeons->hasMorePages()): ?>
                                    <a href="<?php echo e($dungeons->nextPageUrl()); ?>"
                                        class="w-10 h-10 flex items-center justify-center rounded border border-[#514b3c] bg-[#252117] hover:bg-[#2d2820] hover:border-[#a6925e] transition-all duration-300 text-[#a6925e] ml-2 hover:text-[#e5b769] pointer-events-auto">
                                        <span class="text-lg font-medium">→</span>
                                    </a>
                                <?php else: ?>
                                    <button disabled
                                        class="w-10 h-10 flex items-center justify-center rounded border border-[#3d3a32] bg-[#1a1814] text-[#514b3c] cursor-not-allowed opacity-70 ml-2 pointer-events-none">
                                        <span class="text-lg font-medium">→</span>
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                
                <div class="text-center mb-4">
                    <div class="inline-block px-3 py-1 bg-[#1a1814] rounded border-t border-b border-[#514b3c]">
                        <span class="text-[#9a9483] text-xs">
                            Страница <span class="text-[#e5b769]"><?php echo e($dungeons->currentPage()); ?></span> из <span
                                class="text-[#e5b769]"><?php echo e($dungeons->lastPage()); ?></span>
                            <span class="inline-block mx-1 text-[#514b3c]">•</span>
                            Всего: <span class="text-[#e5b769]"><?php echo e($totalCount); ?></span>
                        </span>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    
    <?php if (isset($component)) { $__componentOriginal9e67914025cd6dd9a8c10d111e91463c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9e67914025cd6dd9a8c10d111e91463c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.navigation-buttons','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.navigation-buttons'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9e67914025cd6dd9a8c10d111e91463c)): ?>
<?php $attributes = $__attributesOriginal9e67914025cd6dd9a8c10d111e91463c; ?>
<?php unset($__attributesOriginal9e67914025cd6dd9a8c10d111e91463c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9e67914025cd6dd9a8c10d111e91463c)): ?>
<?php $component = $__componentOriginal9e67914025cd6dd9a8c10d111e91463c; ?>
<?php unset($__componentOriginal9e67914025cd6dd9a8c10d111e91463c); ?>
<?php endif; ?>

    
    <?php if (isset($component)) { $__componentOriginal4766510e0268a7a5917e77b146281554 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4766510e0268a7a5917e77b146281554 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.footer','data' => ['onlineCount' => $onlineCount]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.footer'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['onlineCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($onlineCount)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4766510e0268a7a5917e77b146281554)): ?>
<?php $attributes = $__attributesOriginal4766510e0268a7a5917e77b146281554; ?>
<?php unset($__attributesOriginal4766510e0268a7a5917e77b146281554); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4766510e0268a7a5917e77b146281554)): ?>
<?php $component = $__componentOriginal4766510e0268a7a5917e77b146281554; ?>
<?php unset($__componentOriginal4766510e0268a7a5917e77b146281554); ?>
<?php endif; ?>
</body>

</html><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/dungeons/index.blade.php ENDPATH**/ ?>