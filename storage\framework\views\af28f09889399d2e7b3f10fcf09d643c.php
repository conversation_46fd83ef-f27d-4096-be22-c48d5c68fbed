<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'id' => '',
    'title' => '',
    'icon' => '',
    'description' => '',
    'releaseDate' => '',
    'requirements' => [],
    'plannedEvents' => null
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'id' => '',
    'title' => '',
    'icon' => '',
    'description' => '',
    'releaseDate' => '',
    'requirements' => [],
    'plannedEvents' => null
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>



<div id="<?php echo e($id); ?>" class="fixed inset-0 bg-black bg-opacity-80 z-50 hidden items-center justify-center">
    <div
        class="relative w-11/12 max-w-sm mx-auto bg-gradient-to-b from-[#38352c] to-[#292722] border-2 border-[#a6925e] rounded-md shadow-lg overflow-hidden">
        
        <div
            class="bg-gradient-to-r from-[#3b3a33] to-[#4a4a3d] py-2 px-3 border-b border-[#a6925e] flex items-center justify-between">
            <div class="flex items-center">
                <img src="<?php echo e(asset($icon)); ?>" alt="<?php echo e($title); ?>"
                    class="w-6 h-6 mr-2 filter grayscale opacity-70">
                <h4 class="text-[#e5b769] text-sm font-semibold tracking-wide"><?php echo e($title); ?></h4>
            </div>
            <button type="button" onclick="closeMobileTooltip('<?php echo e($id); ?>')"
                class="bg-[#38352c] text-[#a6925e] hover:text-[#e5b769] text-lg w-6 h-6 flex items-center justify-center rounded-full border border-[#a6925e]">
                &times;
            </button>
        </div>

        <div class="p-3">
            
            <div
                class="inline-flex items-center px-2 py-1 mb-2 rounded-sm bg-[#3b3a33] text-[#a09a8a] text-xs border border-[#a6925e] relative overflow-hidden">
                <span class="relative z-10 font-medium">В разработке</span>
                <span
                    class="absolute inset-0 bg-gradient-to-r from-[#3b3a33] via-[#4a4a3d] to-[#3b3a33] opacity-50 animate-pulse"></span>
            </div>

            
            <div class="bg-[#2a2824] border border-[#5c5749] rounded-sm p-2 mb-2">
                <p class="text-xs text-[#d9d3b8] leading-relaxed">
                    <?php echo e($description); ?>

                </p>
            </div>

            
            <div class="bg-[#2a2824] border border-[#5c5749] rounded-sm p-2 <?php if(!empty($requirements)): ?> mb-2 <?php endif; ?>">
                <div class="flex items-start mb-1">
                    <span class="text-[#a09a8a] text-xs mr-2">⏱</span>
                    <div class="text-xs text-[#e5b769] italic"><?php echo e($releaseDate); ?></div>
                </div>
                <?php if($plannedEvents): ?>
                    <div class="flex items-start">
                        <span class="text-[#a09a8a] text-xs mr-2">📅</span>
                        <div class="text-xs text-[#a09a8a]">Планируемые события: <?php echo e($plannedEvents); ?></div>
                    </div>
                <?php endif; ?>
            </div>

            
            <?php if(!empty($requirements)): ?>
                <div class="bg-[#302e25] border border-[#5c5749] rounded-sm p-2">
                    <div class="text-[#e74c3c] text-xs font-medium mb-1">Требования:</div>
                    <?php $__currentLoopData = $requirements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $requirement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="flex items-start mb-1">
                            <span class="text-[#a09a8a] text-xs mr-2"><?php echo e($requirement['icon']); ?></span>
                            <div class="text-xs text-[#a09a8a]"><?php echo e($requirement['text']); ?></div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/battle/tooltip-modal.blade.php ENDPATH**/ ?>